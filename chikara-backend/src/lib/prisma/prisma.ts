import { PrismaClient } from "@prisma/client";

import questExtensions from "./questExtensions.js";
import shopExtensions from "./shopExtensions.js";
import userExtensions from "./userExtensions.js";
import itemExtensions from "./itemExtensions.js";

// Create and export the extended client
const prismaClient = new PrismaClient()
    .$extends(userExtensions)
    .$extends(shopExtensions)
    .$extends(questExtensions)
    .$extends(itemExtensions);

export default prismaClient;
